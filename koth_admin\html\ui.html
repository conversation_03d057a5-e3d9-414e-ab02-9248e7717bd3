<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KOTH Admin Panel</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div id="admin-panel">
    <div class="panel-header">
      <h1>KOTH Admin Panel</h1>
      <button class="close-btn" id="close-panel">×</button>
    </div>

    <div class="panel-content">
      <!-- KOTH Control Section -->
      <div class="section koth-control">
        <h2>KOTH Round Control</h2>
        <div class="control-buttons">
          <button class="btn btn-success" id="start-round">Start Round</button>
          <button class="btn btn-danger" id="stop-round">Stop Round</button>
          <button class="btn btn-danger" id="delete-vehicles">Delete All Vehicles</button>
          <div class="round-status">
            <span class="status-label">Status:</span>
            <span class="status-value" id="round-status">Inactive</span>
          </div>
        </div>
        <div class="zone-info" id="zone-info">
          <div class="info-item">
            <span class="info-label">Controlling Team:</span>
            <span class="info-value" id="controlling-team">None</span>
          </div>
          <div class="info-item">
            <span class="info-label">Zone Points:</span>
            <div class="zone-points">
              <span class="team-points red">Red: <span id="red-points">0</span></span>
              <span class="team-points green">Green: <span id="green-points">0</span></span>
              <span class="team-points blue">Blue: <span id="blue-points">0</span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- Player Management Section -->
      <div class="section player-management">
        <div class="section-header">
          <h2>Player Management</h2>
          <button class="btn btn-small btn-refresh" id="refresh-players">🔄 Refresh</button>
        </div>

        <div class="search-bar">
          <input type="text" id="player-search" placeholder="Search players...">
        </div>

        <div class="player-list" id="player-list">
          <!-- Players will be populated here -->
        </div>
      </div>
    </div>
  </div>

  <!-- Player Action Modal -->
  <div id="player-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modal-player-name">Player Actions</h3>
        <button class="close-btn" id="close-modal">×</button>
      </div>
      
      <div class="modal-body">
        <div class="player-stats">
          <div class="stat-item">
            <span class="stat-label">ID:</span>
            <span class="stat-value" id="modal-player-id">-</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Money:</span>
            <span class="stat-value">$<span id="modal-player-money">0</span></span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Level:</span>
            <span class="stat-value" id="modal-player-level">1</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">XP:</span>
            <span class="stat-value" id="modal-player-xp">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">K/D:</span>
            <span class="stat-value"><span id="modal-player-kills">0</span>/<span id="modal-player-deaths">0</span></span>
          </div>
        </div>

        <div class="action-section">
          <h4>Give Money</h4>
          <div class="input-group">
            <input type="number" id="money-amount" placeholder="Amount" min="0">
            <button class="btn btn-primary" id="give-money">Give Money</button>
          </div>
        </div>

        <div class="action-section">
          <h4>Give XP</h4>
          <div class="input-group">
            <input type="number" id="xp-amount" placeholder="Amount" min="0">
            <button class="btn btn-primary" id="give-xp">Give XP</button>
          </div>
        </div>

        <div class="action-section">
          <h4>Set Level</h4>
          <div class="input-group">
            <input type="number" id="level-amount" placeholder="Level" min="1" max="50">
            <button class="btn btn-primary" id="set-level">Set Level</button>
          </div>
        </div>

        <div class="action-section danger-zone">
          <h4>Danger Zone</h4>
          <button class="btn btn-danger" id="reset-player">Reset Player Stats</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Notification System -->
  <div id="notification-container"></div>

  <script src="script.js"></script>
</body>
</html>
