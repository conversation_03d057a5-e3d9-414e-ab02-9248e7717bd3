print('[KOTH Admin] Server loading...')

-- Admin check function (you can customize this based on your server's admin system)
local function isAdmin(source)
    -- For now, using a simple identifier check. You can replace this with your admin system
    local identifiers = GetPlayerIdentifiers(source)
    
  -- List of admin identifiers (add your admin identifiers here)
  local adminIdentifiers = {
    "steam:110000105c7f1da", -- Your Steam ID (if you have one)
    "license:c88fdbd3f2660c309932ec8fcd5e3be1eb2a9c4d", -- Your primary license
    "license2:b2d29abf0f03f0411639f54a9c7ceddc5b181319", -- Your secondary license
    "discord:332035384705810432", -- Your Discord ID
    "fivem:4353894", -- Your FiveM ID
    "live:1055518423139464", -- Your Live ID
    "xbl:2535423827211959", -- Your Xbox Live ID
    "ip:*************" -- Your IP (not recommended for production)
  }
    
    for _, id in ipairs(identifiers) do
        for _, adminId in ipairs(adminIdentifiers) do
            if id == adminId then
                return true
            end
        end
    end
    
    -- Admin check is now properly configured
    return false
end

-- Get player identifiers helper
local function GetPlayerTXID(source)
    local identifiers = GetPlayerIdentifiers(source)
    for _, id in ipairs(identifiers) do
        if string.find(id, "license:") then
            return id
        end
    end
    return nil
end

-- Debug command to check identifiers
RegisterCommand('checkidentifiers', function(source, args, rawCommand)
    if source == 0 then
        print('[KOTH Admin] This command can only be used by players')
        return
    end
    
    local identifiers = GetPlayerIdentifiers(source)
    print('[KOTH Admin] Player identifiers for ' .. GetPlayerName(source) .. ':')
    for _, id in ipairs(identifiers) do
        print('  - ' .. id)
    end
end, false)

-- Command to open admin panel
RegisterCommand('kothadmin', function(source, args, rawCommand)
    if source == 0 then
        print('[KOTH Admin] This command can only be used by players')
        return
    end
    
    -- Debug: Print player identifiers
    local identifiers = GetPlayerIdentifiers(source)
    print('[KOTH Admin] Checking admin access for ' .. GetPlayerName(source))
    print('[KOTH Admin] Player identifiers:')
    for _, id in ipairs(identifiers) do
        print('  - ' .. id)
    end
    
    if not isAdmin(source) then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[KOTH Admin]", "You don't have permission to use this command!"}
        })
        return
    end
    
    -- Get all online players for the admin panel
    local players = {}
    
    -- First, get all online players
    local onlinePlayers = {}
    for _, playerId in ipairs(GetPlayers()) do
        local txid = GetPlayerTXID(playerId)
        if txid then
            onlinePlayers[txid] = {
                id = playerId,
                name = GetPlayerName(playerId),
                txid = txid
            }
        end
    end
    
    -- Query database for all player stats
    exports.oxmysql:execute('SELECT * FROM koth_players', {}, function(results)
        if results then
            for _, row in ipairs(results) do
                -- Check if this player is online
                local onlineData = onlinePlayers[row.txid]
                if onlineData then
                    -- Player is online, use their current server ID
                    table.insert(players, {
                        id = onlineData.id,
                        name = onlineData.name,
                        txid = row.txid,
                        money = row.money or 0,
                        xp = row.xp or 0,
                        level = row.level or 1,
                        kills = row.kills or 0,
                        deaths = row.deaths or 0
                    })
                    -- Remove from online players list
                    onlinePlayers[row.txid] = nil
                end
            end
        end
        
        -- Add any online players not in database yet
        for txid, playerInfo in pairs(onlinePlayers) do
            table.insert(players, {
                id = playerInfo.id,
                name = playerInfo.name,
                txid = txid,
                money = 0,
                xp = 0,
                level = 1,
                kills = 0,
                deaths = 0
            })
        end
        
        -- Get KOTH zone status
        local kothStatus = nil
        local success2, result2 = pcall(function()
            return exports['koth_teamsel']:GetKothZoneStatus()
        end)
        if success2 then
            kothStatus = result2
        end
        
        -- Send data to client
        TriggerClientEvent('koth_admin:openPanel', source, {
            players = players,
            kothStatus = kothStatus
        })
    end)
end, false)

-- Give money to player
RegisterNetEvent('koth_admin:giveMoney', function(targetId, amount)
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized giveMoney attempt from', source)
        return
    end
    
    local targetPlayer = tonumber(targetId)
    local moneyAmount = tonumber(amount)
    
    if not targetPlayer or not moneyAmount then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Invalid player ID or amount')
        return
    end
    
    -- Check if player is online
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Player is not online')
        return
    end
    
    -- Use the main resource's export function to properly update cached data
    local success = exports['koth_teamsel']:GivePlayerMoney(targetPlayer, moneyAmount)
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 
            string.format('Gave $%d to %s', moneyAmount, GetPlayerName(targetPlayer)))
        
        -- Log the action
        print(string.format('[KOTH Admin] %s gave $%d to %s (ID: %d)', 
            GetPlayerName(source), moneyAmount, GetPlayerName(targetPlayer), targetPlayer))
            
        -- Log to money transactions
        local txid = GetPlayerTXID(targetPlayer)
        if txid then
            exports.oxmysql:execute('INSERT INTO koth_money_log (txid, transaction_type, amount, balance_before, balance_after, description) VALUES (?, ?, ?, ?, ?, ?)', {
                txid,
                'admin_give',
                moneyAmount,
                0, -- We don't have the before balance easily accessible now
                0, -- We don't have the after balance easily accessible now
                string.format('Admin %s gave money', GetPlayerName(source))
            })
        end
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to give money - player data not loaded')
    end
end)

-- Give XP to player
RegisterNetEvent('koth_admin:giveXP', function(targetId, amount)
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized giveXP attempt from', source)
        return
    end
    
    local targetPlayer = tonumber(targetId)
    local xpAmount = tonumber(amount)
    
    if not targetPlayer or not xpAmount then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Invalid player ID or amount')
        return
    end
    
    -- Check if player is online
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Player is not online')
        return
    end
    
    -- Use the main resource's export function to properly update cached data
    local success = exports['koth_teamsel']:GivePlayerXP(targetPlayer, xpAmount)
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 
            string.format('Gave %d XP to %s', xpAmount, GetPlayerName(targetPlayer)))
        
        -- Log the action
        print(string.format('[KOTH Admin] %s gave %d XP to %s (ID: %d)', 
            GetPlayerName(source), xpAmount, GetPlayerName(targetPlayer), targetPlayer))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to give XP - player data not loaded')
    end
end)

-- Set player level
RegisterNetEvent('koth_admin:setLevel', function(targetId, level)
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized setLevel attempt from', source)
        return
    end
    
    local targetPlayer = tonumber(targetId)
    local newLevel = tonumber(level)
    
    if not targetPlayer or not newLevel then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Invalid player ID or level')
        return
    end
    
    -- Check if player is online
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Player is not online')
        return
    end
    
    -- Use the main resource's export function to properly update cached data
    local success = exports['koth_teamsel']:SetPlayerLevel(targetPlayer, newLevel)
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 
            string.format('Set %s to level %d', GetPlayerName(targetPlayer), newLevel))
        
        -- Log the action
        print(string.format('[KOTH Admin] %s set %s (ID: %d) to level %d', 
            GetPlayerName(source), GetPlayerName(targetPlayer), targetPlayer, newLevel))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to set level - player data not loaded')
    end
end)

-- Start KOTH round
RegisterNetEvent('koth_admin:startRound', function()
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized startRound attempt from', source)
        return
    end
    
    -- Call the main KOTH resource to start a round
    local success = exports['koth_teamsel']:StartKothRound()
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 'KOTH round started!')
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 215, 0},
            multiline = true,
            args = {"[KOTH]", "A new KOTH round has been started by an admin!"}
        })
        
        -- Log the action
        print(string.format('[KOTH Admin] %s started a KOTH round', GetPlayerName(source)))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to start round')
    end
end)

-- Stop KOTH round
RegisterNetEvent('koth_admin:stopRound', function()
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized stopRound attempt from', source)
        return
    end
    
    -- Call the main KOTH resource to stop the round
    local success = exports['koth_teamsel']:StopKothRound()
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 'KOTH round stopped!')
        TriggerClientEvent('chat:addMessage', -1, {
            color = {255, 0, 0},
            multiline = true,
            args = {"[KOTH]", "The KOTH round has been stopped by an admin!"}
        })
        
        -- Log the action
        print(string.format('[KOTH Admin] %s stopped the KOTH round', GetPlayerName(source)))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to stop round')
    end
end)

-- Reset player stats
RegisterNetEvent('koth_admin:resetPlayer', function(targetId)
    local source = source
    
    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized resetPlayer attempt from', source)
        return
    end
    
    local targetPlayer = tonumber(targetId)
    
    if not targetPlayer then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Invalid player ID')
        return
    end
    
    -- Check if player is online
    if GetPlayerPing(targetPlayer) <= 0 then
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Player is not online')
        return
    end
    
    -- Use the main resource's export function to properly update cached data
    local success = exports['koth_teamsel']:ResetPlayerStats(targetPlayer)
    
    if success then
        TriggerClientEvent('koth_admin:notification', source, 'success', 
            string.format('Reset stats for %s', GetPlayerName(targetPlayer)))
        
        -- Log the action
        print(string.format('[KOTH Admin] %s reset stats for %s (ID: %d)', 
            GetPlayerName(source), GetPlayerName(targetPlayer), targetPlayer))
    else
        TriggerClientEvent('koth_admin:notification', source, 'error', 'Failed to reset player - player data not loaded')
    end
end)

-- Refresh player list
RegisterNetEvent('koth_admin:refreshPlayers', function()
    local source = source

    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized refresh attempt from', source)
        return
    end

    -- Get all online players for the admin panel
    local players = {}

    -- First, get all online players
    local onlinePlayers = {}
    for _, playerId in ipairs(GetPlayers()) do
        local txid = GetPlayerTXID(playerId)
        if txid then
            onlinePlayers[txid] = {
                id = playerId,
                name = GetPlayerName(playerId),
                txid = txid
            }
        end
    end

    -- Query database for all player stats
    exports.oxmysql:execute('SELECT * FROM koth_players', {}, function(results)
        if results then
            for _, row in ipairs(results) do
                -- Check if this player is online
                local onlineData = onlinePlayers[row.txid]
                if onlineData then
                    -- Player is online, use their current server ID
                    table.insert(players, {
                        id = onlineData.id,
                        name = onlineData.name,
                        txid = row.txid,
                        money = row.money or 0,
                        xp = row.xp or 0,
                        level = row.level or 1,
                        kills = row.kills or 0,
                        deaths = row.deaths or 0
                    })
                    -- Remove from online players list
                    onlinePlayers[row.txid] = nil
                end
            end
        end

        -- Add any online players not in database yet
        for txid, playerInfo in pairs(onlinePlayers) do
            table.insert(players, {
                id = playerInfo.id,
                name = playerInfo.name,
                txid = txid,
                money = 0,
                xp = 0,
                level = 1,
                kills = 0,
                deaths = 0
            })
        end

        -- Get KOTH zone status
        local kothStatus = nil
        local success2, result2 = pcall(function()
            return exports['koth_teamsel']:GetKothZoneStatus()
        end)
        if success2 then
            kothStatus = result2
        end

        -- Send updated data
        TriggerClientEvent('koth_admin:updateData', source, {
            players = players,
            kothStatus = kothStatus
        })
    end)
end)

-- Delete all vehicles
RegisterNetEvent('koth_admin:deleteAllVehicles', function()
    local source = source

    if not isAdmin(source) then
        print('[KOTH Admin] Unauthorized deleteAllVehicles attempt from', source)
        return
    end

    -- Get all vehicles and delete them
    local vehicleCount = 0
    local allVehicles = GetAllVehicles()

    for i = 1, #allVehicles do
        local vehicle = allVehicles[i]
        if DoesEntityExist(vehicle) then
            -- Check if vehicle has any players in it
            local hasPlayers = false
            for seat = -1, GetVehicleMaxNumberOfPassengers(vehicle) - 1 do
                local ped = GetPedInVehicleSeat(vehicle, seat)
                if ped ~= 0 and IsPedAPlayer(ped) then
                    hasPlayers = true
                    break
                end
            end

            -- Only delete if no players are in the vehicle
            if not hasPlayers then
                DeleteEntity(vehicle)
                vehicleCount = vehicleCount + 1
            end
        end
    end

    -- Send success notification
    TriggerClientEvent('koth_admin:notification', source, 'success',
        string.format('Deleted %d vehicles successfully.', vehicleCount))

    TriggerClientEvent('chat:addMessage', -1, {
        color = {255, 165, 0},
        multiline = true,
        args = {"[KOTH Admin]", string.format("All vehicles have been deleted by an admin! (%d vehicles removed)", vehicleCount)}
    })

    -- Log the action
    print(string.format('[KOTH Admin] %s deleted %d vehicles on the server', GetPlayerName(source), vehicleCount))
end)

print('[KOTH Admin] Server loaded successfully')
