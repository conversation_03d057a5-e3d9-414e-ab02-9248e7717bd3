<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
  
    <Item>
      <modelName>apc</modelName>
      <txdName>apc</txdName>
      <handlingId>APC</handlingId>
      <gameName>APC</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>vehicle</expressionDictName>
      <expressionName>rhino</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_gr_vehicle_weapons</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_TANK_APC</layout>
      <coverBoundOffsets>APC_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_APC_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_APC</bonnetCameraName>
      <povCameraName>VEHICLE_BONNET_CAMERA_APC</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_APC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.510000" />
      <wheelScaleRear value="0.510000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.570000" />
      <envEffScaleMax value="0.590000" />
      <envEffScaleMin2 value="0.570000" />
      <envEffScaleMax2 value="0.590000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.400000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        50.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.771" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="5" />
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_EXTRAS_ALL FLAG_USE_HIGHER_DOOR_TORQUE FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_IS_TANK FLAG_USE_COVERBOUND_INFO_FOR_COVERGEN FLAG_DONT_TIMESLICE_WHEELS FLAG_TURRET_MODS_ON_ROOF FLAG_RESET_TURRET_SEAT_HEADING FLAG_UPDATE_WEAPON_BATTERY_BONES FLAG_DONT_HOLD_LOW_GEARS_WHEN_ENGINE_UNDER_LOAD FLAG_INCREASE_LOW_SPEED_TORQUE FLAG_KEEP_ALL_TURRETS_SYNCHRONISED FLAG_HAS_INCREASED_RAMMING_FORCE FLAG_HOLD_TO_SHUFFLE FLAG_HEADLIGHTS_ON_TAP_ONLY FLAG_HAS_BULLET_RESISTANT_GLASS</flags>
      <type>VEHICLE_TYPE_AMPHIBIOUS_AUTOMOBILE</type>
      <plateType>VPT_NONE</plateType>
      <vehicleClass>VC_MILITARY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>trailersmall2</Item>
      </trailers>
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera />
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="CVehicleModelInfo__CVehicleOverrideRagdollThreshold">
        <MinComponent value="22" />
        <MaxComponent value="22" />
        <ThresholdMult value="1.500000" />
      </pOverrideRagdollThreshold>
    </Item>

  <Item>
      <modelName>mlec</modelName>
      <txdName>mlec</txdName>
      <handlingId>MLEC</handlingId>
      <gameName>MLEC</gameName>
      <vehicleMakeName>MITSU</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>sultan</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SULTAN_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.060000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.025000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.060000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.080000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.295000" z="0.533000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.135000" y="0.175000" z="0.465000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
            <Offset x="0.136000" y="0.126000" z="0.475000" />
            <SeatIndex value="2" />
        </Item>
        <Item>
            <Offset x="0.136000" y="0.126000" z="0.475000" />
            <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="0.000000" y="-0.135000" z="0.670000" />
      <PovCameraVerticalAdjustmentForRollCage value="-0.045000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="-0.150000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.236300" />
      <wheelScaleRear value="0.236300" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.700000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        500.000000
        500.000000
        500.000000
        500.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.832" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="50" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="50" />
      <flags>FLAG_EXTRAS_REQUIRE</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_SULTAN</dashboardType>
      <vehicleClass>VC_SPORT</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes>
        <Item>EXTRA_1 EXTRA_3 EXTRA_5</Item>
      </extraIncludes>
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_SULTAN_FRONT_LEFT</Item>
        <Item>STD_SULTAN_FRONT_RIGHT</Item>
        <Item>STD_SULTAN_REAR_LEFT</Item>
        <Item>STD_SULTAN_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  <Item>
      <modelName>t90ms</modelName>
      <txdName>t90ms</txdName>
      <handlingId>t90ms</handlingId>
      <gameName>T90-MS Tagil</gameName>
      <vehicleMakeName />
      <expressionDictName>vehicle</expressionDictName>
      <expressionName>rhino</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_car</ptfxAssetName>
      <audioNameHash>RHINO</audioNameHash>
      <layout>LAYOUT_TANK</layout>
      <coverBoundOffsets>RHINO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_TANK_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_TANK</bonnetCameraName>
      <povCameraName>VEHICLE_BONNET_CAMERA_TANK</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_TANK_RHINO</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.400000" />
      <wheelScaleRear value="0.400000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.700000" />
      <envEffScaleMin value="0.570000" />
      <envEffScaleMax value="0.590000" />
      <envEffScaleMin2 value="0.570000" />
      <envEffScaleMax2 value="0.590000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        50.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.771" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_2</swankness>
      <maxNum value="5" />
      <flags>FLAG_NO_BOOT FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_EXTRAS_ALL FLAG_DONT_CLOSE_DOOR_UPON_EXIT FLAG_USE_HIGHER_DOOR_TORQUE FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_IS_TANK FLAG_USE_COVERBOUND_INFO_FOR_COVERGEN FLAG_DONT_TIMESLICE_WHEELS</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <vehicleClass>VC_MILITARY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_Y_Marine_03</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Marine_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera />
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="CVehicleModelInfo__CVehicleOverrideRagdollThreshold">
        <MinComponent value="22" />
        <MaxComponent value="22" />
        <ThresholdMult value="1.500000" />
      </pOverrideRagdollThreshold>
    </Item>
  <Item>
      <modelName>mh60l</modelName>
      <txdName>mh60l</txdName>
      <handlingId>MH60L</handlingId>
      <gameName>MH60L</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_heli</ptfxAssetName>
      <audioNameHash>HUNTER</audioNameHash>
      <layout>LAYOUT_HELI_MH60L</layout>
      <coverBoundOffsets>VALKYRIE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_HELI_CAMERA</cameraName>
      <aimCameraName>MAVERICK_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>ANNIHILATOR_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>HELI_POV_CAMERA</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_VALKYRIE</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.035000" y="-0.215000" z="-0.018000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.083000" z="-0.028000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.165000" y="0.345000" z="0.490000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.190000" y="0.283000" z="0.448000" />
      <PovCameraOffset x="-0.050000" y="-0.020000" z="0.645000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.055000" />
      <vfxInfoName>VFXVEHICLEINFO_HELI_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.080000" />
      <wheelScaleRear value="0.080000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.200000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.200000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000   
        90.000000   
        130.000000  
        260.000000  
        750.000000  
        750.000000  
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.500000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="999" />
      <flags>FLAG_NO_BOOT FLAG_LAW_ENFORCEMENT FLAG_DRIVER_NO_DRIVE_BY FLAG_NO_RESPRAY FLAG_HAS_LIVERY FLAG_ALLOWS_RAPPEL FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_DONT_TIMESLICE_WHEELS FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_USE_TURRET_RELATIVE_AIM_CALCULATION</flags>
      <type>VEHICLE_TYPE_HELI</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_MAVERICK</dashboardType>
      <vehicleClass>VC_HELICOPTER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>s_m_y_swat_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards>
        <Item>REWARD_WEAPON_SNIPERRIFLE</Item>
        <Item>REWARD_AMMO_SNIPERRIFLE</Item>
        <Item>REWARD_STAT_WEAPON</Item>
      </rewards>
      <cinematicPartCamera>
        <Item>HELI_REAR_ROTOR_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="-0.500000" />
      <buoyancySphereSizeScale value="0.900000" />
      <pOverrideRagdollThreshold type="NULL" />
    </Item>
  <Item>
      <modelName>bell360</modelName>
      <txdName>bell360</txdName>
      <handlingId>bell360</handlingId>
      <gameName>bell360</gameName>
      <vehicleMakeName>BELLB</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>HUNTER</audioNameHash>
      <!--layout>LAYOUT_HELI_VIPER</layout-->
      <layout>LAYOUT_HELI_BELL360</layout>
      <coverBoundOffsets>STANDARD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_HELI_CAMERA</cameraName>
      <aimCameraName>HELI_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>FROGGER_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>HELI_SWIFT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.123000" y="0.423000" z="0.453000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.199000" y="0.471000" z="0.313000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
            <Offset x="-0.550000" y="-0.160000" z="0.495000" />
            <SeatIndex value="2" />
        </Item>
        <Item>
            <Offset x="0.525000" y="-0.323000" z="0.413000" />
            <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="0.085000" z="0.845000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.060000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.060000" />
      <vfxInfoName>VFXVEHICLEINFO_HELI_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.092000" />
      <wheelScaleRear value="0.092000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.300000" />
      <envEffScaleMax value="0.500000" />
      <envEffScaleMin2 value="0.300000" />
      <envEffScaleMax2 value="0.500000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x32000000" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        40.000000
        100.000000
        200.000000
        1000.000000
        1000.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.350000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_3</swankness>
      <maxNum value="999" />
      <flags>FLAG_NO_BOOT FLAG_DRIVER_NO_DRIVE_BY FLAG_HELICOPTER_WITH_LANDING_GEAR FLAG_NO_RESPRAY FLAG_AVERAGE_CAR FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_DONT_TIMESLICE_WHEELS FLAG_HELI_USES_FIXUPS_ON_OPEN_DOOR FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_DISABLE_WEAPON_WHEEL_IN_FIRST_PERSON FLAG_USE_TURRET_RELATIVE_AIM_CALCULATION FLAG_USE_PILOT_HELMET FLAG_HAS_CAPPED_EXPLOSION_DAMAGE FLAG_USE_AIRCRAFT_STYLE_WEAPON_TARGETING</flags>
      <type>VEHICLE_TYPE_HELI</type>
      <plateType>VPT_NONE</plateType>
    <dashboardType>VDT_LAZER</dashboardType>
      <vehicleClass>VC_HELICOPTER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_Gentransport</driverName>
          <npcName>Pilot</npcName>
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>HELI_REAR_ROTOR_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_FRONT_LEFT</Item>
        <Item>STD_FRONT_RIGHT</Item>
        <Item>HELI_LEFT_SIDE_PASSENGER</Item>
        <Item>HELI_RIGHT_SIDE_PASSENGER</Item>
      </firstPersonDrivebyData>
    </Item>
  <Item>
      <modelName>17cheyenne2</modelName>
      <txdName>17cheyenne2</txdName>
      <handlingId>17CHEYENNE2</handlingId>
      <gameName>17CHEYENNE2</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_car</ptfxAssetName>
      <audioNameHash>SHERIFF2</audioNameHash>
      <layout>LAYOUT_VAN_ARMORED</layout>
      <coverBoundOffsets>INSURGENT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_INSURGENT</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.083000" y="0.018000" z="0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.125000" y="-0.008000" z="-0.096000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.083000" y="-0.022000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.055000" z="-0.063000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.235000" z="0.483000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.172000" y="0.284000" z="0.413000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.404000" y="0.392000" z="0.524000" />
          <SeatIndex value="6" />
        </Item>
        <Item>
          <Offset x="0.142000" y="0.464000" z="0.553000" />
          <SeatIndex value="7" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.580000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.322900" />
      <wheelScaleRear value="0.322900" />
      <dirtLevelMin value="0.900000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000	
        90.000000	
        130.000000	
        260.000000	
        750.000000	
        750.000000	
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_CONSIDERED_FOR_VEHICLE_ENTRY_WHEN_STOOD_ON FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_PREFER_ENTER_TURRET_AFTER_DRIVER FLAG_HAS_DIRECTIONAL_SHUFFLES FLAG_USE_SMALLER_OPEN_DOOR_RATIO_TOLERANCE FLAG_DONT_STOP_WHEN_GOING_TO_CLIMB_UP_POINT FLAG_DISABLE_BUSTING FLAG_HAS_LIVERY </flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCKDIGI</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <doorStiffnessMultipliers>
        <Item>
          <doorId>VEH_EXT_BOOT</doorId>
	  <stiffnessMult value="19.5" />
        </Item>
      </doorStiffnessMultipliers>
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_INSURGENT_FRONT_LEFT</Item>
        <Item>VAN_INSURGENT_FRONT_RIGHT</Item>
        <Item>VAN_INSURGENT_REAR_LEFT</Item>
        <Item>VAN_INSURGENT_REAR_RIGHT</Item>
        <Item>HANGING_INSURGENT_LEFT</Item>
        <Item>HANGING_INSURGENT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
  </Item>
	<Item>
      <modelName>cfmotoz10</modelName>
      <txdName>cfmotoz10</txdName>
      <handlingId>cfmotoz10</handlingId>
      <gameName>cfmotoz10</gameName>
      <vehicleMakeName>CFMOTO</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>blazer4</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>SANDKING_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <FirstPersonDriveByIKOffset x="0.020000" y="-0.060000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.075000" y="-0.128000" z="-0.020000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.020000" y="0.060000" z="-0.090000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="-0.055000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="-0.055000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.013000" y="-0.028000" z="-0.020000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="-0.040000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
	  <FirstPersonMobilePhoneOffset x="0.143000" y="0.233000" z="0.508000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.490000" />
      <PovCameraOffset x="0.000000" y="-0.125000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.040000" z="0.085000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.040000" z="0.085000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.328500" />
      <wheelScaleRear value="0.328500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="100.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        500.000000
        1000.000000
        2000.000000
        3000.000000
        4000.000000
        5000.000000
      </lodDistances>
      <minSeatHeight value="1.006" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_GULL_WING_DOORS FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_RACE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers>
				<Item>trailersmall</Item>
				<Item>boattrailer</Item>
				<Item>trailersmall</Item>
				<Item>cartrailer</Item>
				<Item>ctrailer</Item>
				<Item>thauler</Item>
				<Item>chauler</Item>
				<Item>shauler</Item>
				<Item>cotrailer</Item>
				<Item>nbtrailer</Item>
				<Item>20fttrailer</Item>
				<Item>trailercast</Item>
				<Item>foxtr1</Item>
				<Item>codestalker</Item>
				<Item>boattrailer2</Item>
				<Item>sstrailer</Item>
				<Item>btrailer</Item>
				<Item>yftrailer</Item>
				<Item>camperman</Item>
				<Item>ehauler</Item>
				<Item>seadoohauler</Item>
				<Item>ptrailer</Item>
				<Item>formtrl</Item>
				<Item>uhauler</Item>
				<Item>bigtexb</Item>
				<Item>8320b</Item>
				<Item>mtrailer</Item>
				<Item>ktrailer</Item>
				<Item>blomenroehr</Item>

	</trailers>
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>RANGER_RANCHERXL_FRONT_LEFT</Item>
        <Item>RANGER_SANDKING_FRONT_RIGHT</Item>
        <Item>RANGER_CRUSADER_REAR_LEFT</Item>
        <Item>RANGER_CRUSADER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>winkyc</modelName>
      <txdName>winkyc</txdName>
      <handlingId>WINKYC</handlingId>
      <gameName>CleanWinky</gameName>
      <vehicleMakeName>VAPID</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>CRUSADER</audioNameHash>
      <layout>LAYOUT_STANDARD</layout>
      <coverBoundOffsets>WINKY_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_JEEP_CAMERA</cameraName>
      <aimCameraName>JEEP_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>REDUCED_NEAR_CLIP_POV_CAMERA_LOOKAROUND</povCameraName>
      <FirstPersonDriveByIKOffset x="0.030000" y="-0.050000" z="-0.005000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="-0.020000" />
      <FirstPersonProjectileDriveByIKOffset x="0.025000" y="-0.130000" z="-0.060000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.010000" z="-0.030000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.025000" y="-0.130000" z="-0.060000" />
      <FirstPersonDriveByRightPassengerIKOffset x="-0.050000" y="-0.43000" z="-0.030000"/>
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="-0.020000" y="-0.040000" z="-0.030000" />
      <FirstPersonMobilePhoneOffset x="0.110000" y="0.148000" z="0.490000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.240000" y="0.273000" z="0.420000" />
      <FirstPersonMobilePhoneSeatIKOffset />
      <PovCameraOffset x="-0.025000" y="-0.145000" z="0.570000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.050000" z="0.050000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.050000" z="0.02000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.300000" />
      <wheelScaleRear value="0.300000" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.900000" />
      <damageOffsetScale value="0.900000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.006" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_SPOILER_MOD_DOESNT_INCREASE_GRIP</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>JEEP_WINKY_FRONT_LEFT</Item>
        <Item>JEEP_WINKY_FRONT_RIGHT</Item>
        <Item>JEEP_WINKY_REAR</Item> 
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pavelow</modelName>
      <txdName>pavelow</txdName>
      <handlingId>pavelow</handlingId>
      <gameName>CARGOBOB</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>HUNTER</audioNameHash>
      <layout>LAYOUT_HELI_PAVELOW</layout>
      <coverBoundOffsets>STANDARD_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_SKYLIFT_CAMERA</cameraName>
      <aimCameraName>SKYLIFT_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>SKYLIFT_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>HELI_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.170000" y="0.355000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.271000" z="0.423000" />
      <PovCameraOffset x="0.000000" y="-0.040000" z="0.700000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_HELI_SKYLIFT</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.200100" />
      <wheelScaleRear value="0.117600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.800000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.800000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        40.000000
        120.000000
        240.000000
        1000.000000
        1000.000000
      </lodDistances>
      <minSeatHeight value="1.728" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="999" />
      <flags>FLAG_NO_BOOT FLAG_PEDS_CAN_STAND_ON_TOP FLAG_HAS_CAPPED_EXPLOSION_DAMAGE FLAG_HELICOPTER_WITH_LANDING_GEAR FLAG_HEADLIGHTS_ON_LANDINGGEAR FLAG_HAS_INTERIOR_EXTRAS FLAG_DRIVER_NO_DRIVE_BY FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_DONT_TIMESLICE_WHEELS</flags>
      <type>VEHICLE_TYPE_HELI</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_LAZER</dashboardType>
      <vehicleClass>VC_HELICOPTER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_DOOR_DSIDE_F</Item>
        <Item>VEH_EXT_DOOR_PSIDE_F</Item>
        <Item>VEH_EXT_DOOR_DSIDE_R</Item>
		<Item>VEH_EXT_DOOR_PSIDE_R</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors>
        <Item>VEH_EXT_DOOR_PSIDE_F</Item>
        <Item>VEH_EXT_DOOR_DSIDE_R</Item>
		<Item>VEH_EXT_DOOR_PSIDE_R</Item>
      </driveableDoors>
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>HELI_REAR_ROTOR_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_FRONT_LEFT</Item>
        <Item>HELI_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>pbus</modelName>
      <txdName>pbus</txdName>
      <handlingId>PBUS</handlingId>
      <gameName>PBUS</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_PRISON_BUS</layout>
      <coverBoundOffsets>PBUS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>DEFAULT_VEHICLE_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>BUS_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.133000" y="0.176000" z="0.508000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.163000" z="0.570000" />
      <PovCameraOffset x="0.000000" y="-0.165000" z="0.630000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.100000" z="0.185000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.323700" />
      <wheelScaleRear value="0.323700" />
      <dirtLevelMin value="0.300000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        35.000000
        90.000000
        180.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="1.513" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_BIG FLAG_AVOID_TURNS FLAG_DONT_SPAWN_IN_CARGEN FLAG_USE_FAT_INTERIOR_LIGHT FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	    <dashboardType>VDT_TRUCK</dashboardType>
      <vehicleClass>VC_EMERGENCY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_PrisGuard_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	    <firstPersonDrivebyData>
        <Item>BUS_PRISON_DRIVER</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>unarmed1</modelName>
      <txdName>unarmed1</txdName>
      <handlingId>unarmed1</handlingId>
      <gameName>UNARMED</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_car</ptfxAssetName>
      <audioNameHash>INSURGENT</audioNameHash>
      <layout>LAYOUT_VAN_ARMORED</layout>
      <coverBoundOffsets>INSURGENT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_INSURGENT</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.083000" y="0.018000" z="0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.125000" y="-0.008000" z="-0.096000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.083000" y="-0.022000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.055000" z="-0.063000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.235000" z="0.483000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.172000" y="0.284000" z="0.413000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.404000" y="0.392000" z="0.524000" />
          <SeatIndex value="6" />
        </Item>
        <Item>
          <Offset x="0.142000" y="0.464000" z="0.553000" />
          <SeatIndex value="7" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.580000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <PovRearPassengerCameraOffset x="-0.015000" y="0.030000" z="0.055000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.322900" />
      <wheelScaleRear value="0.322900" />
      <dirtLevelMin value="0.900000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_CAPPED_EXPLOSION_DAMAGE FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_CONSIDERED_FOR_VEHICLE_ENTRY_WHEN_STOOD_ON FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_PREFER_ENTER_TURRET_AFTER_DRIVER FLAG_HAS_DIRECTIONAL_SHUFFLES FLAG_USE_SMALLER_OPEN_DOOR_RATIO_TOLERANCE FLAG_DONT_STOP_WHEN_GOING_TO_CLIMB_UP_POINT FLAG_DISABLE_BUSTING</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
      </trailers>
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_Marine_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Marine_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <doorStiffnessMultipliers>
        <Item>
          <doorId>VEH_EXT_BOOT</doorId>
	  <stiffnessMult value="19.5" />
        </Item>
      </doorStiffnessMultipliers>
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_INSURGENT_FRONT_LEFT</Item>
        <Item>VAN_INSURGENT_FRONT_RIGHT</Item>
        <Item>VAN_INSURGENT_REAR_LEFT</Item>
        <Item>VAN_INSURGENT_REAR_RIGHT</Item>
        <Item>HANGING_INSURGENT_LEFT</Item>
        <Item>HANGING_INSURGENT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>unarmed2</modelName>
      <txdName>unarmed2</txdName>
      <handlingId>unarmed2</handlingId>
      <gameName>UNARMED</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_car</ptfxAssetName>
      <audioNameHash>INSURGENT</audioNameHash>
      <layout>LAYOUT_VAN_ARMORED</layout>
      <coverBoundOffsets>INSURGENT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_INSURGENT</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.083000" y="0.018000" z="0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.125000" y="-0.008000" z="-0.096000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.083000" y="-0.022000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.055000" z="-0.063000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.235000" z="0.483000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.172000" y="0.284000" z="0.413000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.404000" y="0.392000" z="0.524000" />
          <SeatIndex value="6" />
        </Item>
        <Item>
          <Offset x="0.142000" y="0.464000" z="0.553000" />
          <SeatIndex value="7" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.580000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <PovRearPassengerCameraOffset x="-0.015000" y="0.030000" z="0.055000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.322900" />
      <wheelScaleRear value="0.322900" />
      <dirtLevelMin value="0.900000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_CAPPED_EXPLOSION_DAMAGE FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_CONSIDERED_FOR_VEHICLE_ENTRY_WHEN_STOOD_ON FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_PREFER_ENTER_TURRET_AFTER_DRIVER FLAG_HAS_DIRECTIONAL_SHUFFLES FLAG_USE_SMALLER_OPEN_DOOR_RATIO_TOLERANCE FLAG_DONT_STOP_WHEN_GOING_TO_CLIMB_UP_POINT FLAG_DISABLE_BUSTING</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
      </trailers>
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_Marine_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Marine_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <doorStiffnessMultipliers>
        <Item>
          <doorId>VEH_EXT_BOOT</doorId>
	  <stiffnessMult value="19.5" />
        </Item>
      </doorStiffnessMultipliers>
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_INSURGENT_FRONT_LEFT</Item>
        <Item>VAN_INSURGENT_FRONT_RIGHT</Item>
        <Item>VAN_INSURGENT_REAR_LEFT</Item>
        <Item>VAN_INSURGENT_REAR_RIGHT</Item>
        <Item>HANGING_INSURGENT_LEFT</Item>
        <Item>HANGING_INSURGENT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

   <Item>
      <modelName>hmvs</modelName>
      <txdName>hmvs</txdName>
      <handlingId>hmvs</handlingId>
      <gameName>HMVSPECIAL</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_car</ptfxAssetName>
      <audioNameHash>INSURGENT</audioNameHash>
      <layout>LAYOUT_VAN_ARMORED</layout>
      <coverBoundOffsets>INSURGENT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_INSURGENT</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.083000" y="0.018000" z="0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.125000" y="-0.008000" z="-0.096000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.083000" y="-0.022000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.055000" z="-0.063000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.235000" z="0.483000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.172000" y="0.284000" z="0.413000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.404000" y="0.392000" z="0.524000" />
          <SeatIndex value="6" />
        </Item>
        <Item>
          <Offset x="0.142000" y="0.464000" z="0.553000" />
          <SeatIndex value="7" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.580000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <PovRearPassengerCameraOffset x="-0.015000" y="0.030000" z="0.055000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.322900" />
      <wheelScaleRear value="0.322900" />
      <dirtLevelMin value="0.900000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_CAPPED_EXPLOSION_DAMAGE FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_CONSIDERED_FOR_VEHICLE_ENTRY_WHEN_STOOD_ON FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_PREFER_ENTER_TURRET_AFTER_DRIVER FLAG_HAS_DIRECTIONAL_SHUFFLES FLAG_USE_SMALLER_OPEN_DOOR_RATIO_TOLERANCE FLAG_DONT_STOP_WHEN_GOING_TO_CLIMB_UP_POINT FLAG_DISABLE_BUSTING</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
      </trailers>
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <doorStiffnessMultipliers>
        <Item>
          <doorId>VEH_EXT_BOOT</doorId>
	  <stiffnessMult value="19.5" />
        </Item>
      </doorStiffnessMultipliers>
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_INSURGENT_FRONT_LEFT</Item>
        <Item>VAN_INSURGENT_FRONT_RIGHT</Item>
        <Item>VAN_INSURGENT_REAR_LEFT</Item>
        <Item>VAN_INSURGENT_REAR_RIGHT</Item>
        <Item>HANGING_INSURGENT_LEFT</Item>
        <Item>HANGING_INSURGENT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>uparmor</modelName>
      <txdName>uparmor</txdName>
      <handlingId>uparmor</handlingId>
      <gameName>UPARMOR</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_car</ptfxAssetName>
      <audioNameHash>INSURGENT</audioNameHash>
      <layout>LAYOUT_VAN_ARMORED</layout>
      <coverBoundOffsets>INSURGENT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_INSURGENT</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.083000" y="0.018000" z="0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.125000" y="-0.008000" z="-0.096000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.083000" y="-0.022000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.055000" z="-0.063000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.235000" z="0.483000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.172000" y="0.284000" z="0.413000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.404000" y="0.392000" z="0.524000" />
          <SeatIndex value="6" />
        </Item>
        <Item>
          <Offset x="0.142000" y="0.464000" z="0.553000" />
          <SeatIndex value="7" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.580000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <PovRearPassengerCameraOffset x="-0.015000" y="0.030000" z="0.055000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.322900" />
      <wheelScaleRear value="0.322900" />
      <dirtLevelMin value="0.900000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_CAPPED_EXPLOSION_DAMAGE FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_CONSIDERED_FOR_VEHICLE_ENTRY_WHEN_STOOD_ON FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_PREFER_ENTER_TURRET_AFTER_DRIVER FLAG_HAS_DIRECTIONAL_SHUFFLES FLAG_USE_SMALLER_OPEN_DOOR_RATIO_TOLERANCE FLAG_DONT_STOP_WHEN_GOING_TO_CLIMB_UP_POINT FLAG_DISABLE_BUSTING</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
      </trailers>
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <doorStiffnessMultipliers>
        <Item>
          <doorId>VEH_EXT_BOOT</doorId>
	  <stiffnessMult value="19.5" />
        </Item>
      </doorStiffnessMultipliers>
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_INSURGENT_FRONT_LEFT</Item>
        <Item>VAN_INSURGENT_FRONT_RIGHT</Item>
        <Item>VAN_INSURGENT_REAR_LEFT</Item>
        <Item>VAN_INSURGENT_REAR_RIGHT</Item>
        <Item>HANGING_INSURGENT_LEFT</Item>
        <Item>HANGING_INSURGENT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>uparmorw</modelName>
      <txdName>uparmorw</txdName>
      <handlingId>uparmorw</handlingId>
      <gameName>UPARMORWL</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_car</ptfxAssetName>
      <audioNameHash>INSURGENT</audioNameHash>
      <layout>LAYOUT_VAN_ARMORED</layout>
      <coverBoundOffsets>INSURGENT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_INSURGENT</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.083000" y="0.018000" z="0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.125000" y="-0.008000" z="-0.096000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.083000" y="-0.022000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.055000" z="-0.063000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.235000" z="0.483000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.172000" y="0.284000" z="0.413000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.404000" y="0.392000" z="0.524000" />
          <SeatIndex value="6" />
        </Item>
        <Item>
          <Offset x="0.142000" y="0.464000" z="0.553000" />
          <SeatIndex value="7" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.580000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <PovRearPassengerCameraOffset x="-0.015000" y="0.030000" z="0.055000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.322900" />
      <wheelScaleRear value="0.322900" />
      <dirtLevelMin value="0.900000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_CAPPED_EXPLOSION_DAMAGE FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_CONSIDERED_FOR_VEHICLE_ENTRY_WHEN_STOOD_ON FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_PREFER_ENTER_TURRET_AFTER_DRIVER FLAG_HAS_DIRECTIONAL_SHUFFLES FLAG_USE_SMALLER_OPEN_DOOR_RATIO_TOLERANCE FLAG_DONT_STOP_WHEN_GOING_TO_CLIMB_UP_POINT FLAG_DISABLE_BUSTING</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
      </trailers>
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <doorStiffnessMultipliers>
        <Item>
          <doorId>VEH_EXT_BOOT</doorId>
	  <stiffnessMult value="19.5" />
        </Item>
      </doorStiffnessMultipliers>
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_INSURGENT_FRONT_LEFT</Item>
        <Item>VAN_INSURGENT_FRONT_RIGHT</Item>
        <Item>VAN_INSURGENT_REAR_LEFT</Item>
        <Item>VAN_INSURGENT_REAR_RIGHT</Item>
        <Item>HANGING_INSURGENT_LEFT</Item>
        <Item>HANGING_INSURGENT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>m9395</modelName>
      <txdName>m9395</txdName>
      <handlingId>m9395</handlingId>
      <gameName>BARRACKS2</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>BARRACKS</audioNameHash>
      <layout>LAYOUT_TRUCK_BARRACKS</layout>
      <coverBoundOffsets>BARRACKS2_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_LOW</povCameraName>
      <FirstPersonDriveByIKOffset x="0.063000" y="-0.155000" z="-0.006000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.033000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.092000" y="0.035000" z="-0.080000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.028000" z="-0.078000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.028000" z="-0.078000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.018000" y="-0.045000" z="-0.023000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="-0.050000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.108000" z="-0.013000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.360000" z="0.423000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.213000" y="0.340000" z="0.420000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.163000" y="0.358000" z="0.573000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.376000" y="0.378000" z="0.620000" />
			<SeatIndex value="3" />
		</Item>
        <Item>
			<Offset x="0.163000" y="0.358000" z="0.573000" />
			<SeatIndex value="4" />
		</Item>
		<Item>
			<Offset x="0.376000" y="0.378000" z="0.620000" />
			<SeatIndex value="5" />
		</Item>
        <Item>
			<Offset x="0.163000" y="0.358000" z="0.573000" />
			<SeatIndex value="6" />
		</Item>
		<Item>
			<Offset x="0.376000" y="0.378000" z="0.620000" />
			<SeatIndex value="7" />
		</Item>
        <Item>
			<Offset x="0.163000" y="0.358000" z="0.573000" />
			<SeatIndex value="8" />
		</Item>
		<Item>
			<Offset x="0.376000" y="0.378000" z="0.620000" />
			<SeatIndex value="9" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="-0.020000" y="-0.025000" z="0.545000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.100000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.100000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.382700" />
      <wheelScaleRear value="0.382700" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000
        90.000000
        130.000000
        260.000000
        750.000000
        750.000000
      </lodDistances>
      <minSeatHeight value="0.969" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1500.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_BIG FLAG_HAS_LIVERY FLAG_HAS_INTERIOR_EXTRAS FLAG_EXTRAS_STRONG FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_CAN_HONK_WHEN_FLEEING FLAG_AVOID_TURNS FLAG_USE_FAT_INTERIOR_LIGHT FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_DUKES</dashboardType>
      <vehicleClass>VC_MILITARY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_Marine_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Marine_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_BARRACKS2_FRONT_LEFT</Item>
        <Item>TRUCK_REVERSE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>chinook</modelName>
      <txdName>chinook</txdName>
      <handlingId>CARGOBOB</handlingId>
      <gameName>chinook</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_HELI_CARGOBOB</layout>
      <coverBoundOffsets>CARGOBOB_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_HELI_CAMERA</cameraName>
      <aimCameraName>CARGOBOB_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>CARGOBOB_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>HELI_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.043000" z="-0.073000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.125000" y="0.365000" z="0.603000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.174000" y="0.301000" z="0.463000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="-0.654000" y="-0.143000" z="0.543000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.635000" y="-0.465000" z="0.518000" />
			<SeatIndex value="3" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.080000" z="0.755000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.050000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_HELI_CARGOBOB</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.200100" />
      <wheelScaleRear value="0.117600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.300000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.300000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        40.000000
        120.000000
        240.000000
        1000.000000
        1000.000000
      </lodDistances>
      <minSeatHeight value="1.186" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.500000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="999" />
      <flags>FLAG_NO_BOOT FLAG_DONT_ROTATE_TAIL_ROTOR FLAG_PEDS_CAN_STAND_ON_TOP FLAG_DRIVER_NO_DRIVE_BY FLAG_NO_RESPRAY FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_CAN_BE_DRIVEN_ON FLAG_DONT_TIMESLICE_WHEELS</flags>
      <type>VEHICLE_TYPE_HELI</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_LAZER</dashboardType>
      <vehicleClass>VC_HELICOPTER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_Pilot_02</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_DOOR_DSIDE_R</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>CARGOBOB_RIGHT_REAR_ROTOR_CAMERA</Item>
        <Item>CARGOBOB_LEFT_REAR_ROTOR_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_FRONT_LEFT</Item>
        <Item>HELI_FRONT_RIGHT</Item>
        <Item>HELI_LEFT_SIDE_PASSENGER</Item>
        <Item>HELI_RIGHT_SIDE_PASSENGER</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>rcmcqueen</modelName>
      <txdName>rcmcqueen</txdName>
      <handlingId>rcmcqueen</handlingId>
      <gameName>rcmcqueen</gameName>
      <vehicleMakeName>BENEFAC</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>rcbandito</audioNameHash>
      <layout>LAYOUT_STD_PANTO</layout>
      <coverBoundOffsets>PANTO_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.088000" y="-0.103000" z="-0.080000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.063000" y="-0.115000" z="-0.035000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.035000" z="-0.030000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.153000" y="0.228000" z="0.460000" />
      <FirstPersonMobilePhoneOffset x="0.103000" y="0.270000" z="0.545000" />
      <PovCameraOffset x="0.000000" y="-0.125000" z="0.685000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.191700" />
      <wheelScaleRear value="0.191700" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.800000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="0.300000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        60.000000
        120.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.884" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="10" />
      <flags>FLAG_EXTRAS_ALL FLAG_EXTRAS_STRONG FLAG_POOR_CAR</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_CAVALCADE</dashboardType>
      <vehicleClass>VC_COMPACT</vehicleClass>
      <wheelType>VWT_TUNER</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>PANTO_FRONT_LEFT</Item>
        <Item>PANTO_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>Kart3</modelName>
      <txdName>Kart3</txdName>
      <handlingId>Kart3</handlingId>
      <gameName>Kart3</gameName>
      <vehicleMakeName>STS</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>DUNE4</audioNameHash>
      <layout>LAYOUT_LOW_DUNE</layout>
      <coverBoundOffsets>DUNE_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.020000" z="0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="-0.140000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="-0.020000" z="0.020000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.100000" z="0.000000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.035000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.140000" y="0.173000" z="0.561000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.138000" z="0.438000" />
      <PovCameraOffset x="0.000000" y="-0.225000" z="0.655000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_MOTORBIKE_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.208500" />
      <wheelScaleRear value="0.232200" />
      <dirtLevelMin value="0.300000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        10.000000
        25.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.886" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_NO_BOOT FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_DONT_TIMESLICE_WHEELS FLAG_PEDS_INSIDE_CAN_BE_SET_ON_FIRE_MP</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VVWT_OFFROAD</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.200000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>LOW_DUNE_FRONT_LEFT</Item>
        <Item>LOW_DUNE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
    <Item>
      <modelName>kitam</modelName>
      <txdName>kitam</txdName>
      <handlingId>KITAM</handlingId>
      <gameName>KITAM</gameName>
      <vehicleMakeName>HVY</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>veh_mounted_turrets_car</ptfxAssetName>
      <audioNameHash>RIOT</audioNameHash>
      <layout>LAYOUT_VAN_ARMORED</layout>
      <coverBoundOffsets>INSURGENT_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>MID_BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_STANDARD</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_LOOKAROUND_MID</povCameraName>
      <povTurretCameraName>POV_TURRET_CAMERA_INSURGENT</povTurretCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.083000" y="0.018000" z="0.020000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="-0.125000" y="-0.008000" z="-0.096000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.083000" y="-0.022000" z="-0.010000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.055000" z="-0.063000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.040000" z="-0.020000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="-0.013000" z="-0.020000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.146000" y="0.235000" z="0.483000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.172000" y="0.284000" z="0.413000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
          <Offset x="0.404000" y="0.392000" z="0.524000" />
          <SeatIndex value="6" />
        </Item>
        <Item>
          <Offset x="0.142000" y="0.464000" z="0.553000" />
          <SeatIndex value="7" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.100000" z="0.580000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.030000" z="0.070000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_OFFROAD</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.322900" />
      <wheelScaleRear value="0.322900" />
      <dirtLevelMin value="0.900000" />
      <dirtLevelMax value="1.000000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        20.000000	
        90.000000	
        130.000000	
        260.000000	
        750.000000	
        750.000000	
      </lodDistances>
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="30" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="5" />
      <flags>FLAG_AVERAGE_CAR FLAG_POOR_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_IS_BULKY FLAG_HAS_TURRET_SEAT_ON_VEHICLE FLAG_CONSIDERED_FOR_VEHICLE_ENTRY_WHEN_STOOD_ON FLAG_EXTRAS_ONLY_BREAK_WHEN_DESTROYED FLAG_PREFER_ENTER_TURRET_AFTER_DRIVER FLAG_HAS_DIRECTIONAL_SHUFFLES FLAG_USE_SMALLER_OPEN_DOOR_RATIO_TOLERANCE FLAG_DONT_STOP_WHEN_GOING_TO_CLIMB_UP_POINT FLAG_DISABLE_BUSTING FLAG_HAS_LIVERY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_TRUCKDIGI</dashboardType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <doorStiffnessMultipliers>
        <Item>
          <doorId>VEH_EXT_BOOT</doorId>
	  <stiffnessMult value="19.5" />
        </Item>
      </doorStiffnessMultipliers>
      <needsRopeTexture value="false" />
      <requiredExtras>EXTRA_1</requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_WIDE_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_WIDE_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>VAN_INSURGENT_FRONT_LEFT</Item>
        <Item>VAN_INSURGENT_FRONT_RIGHT</Item>
        <Item>VAN_INSURGENT_REAR_LEFT</Item>
        <Item>VAN_INSURGENT_REAR_RIGHT</Item>
        <Item>HANGING_INSURGENT_LEFT</Item>
        <Item>HANGING_INSURGENT_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>typhoon</modelName>
      <txdName>typhoon</txdName>
      <handlingId>TYPHOON</handlingId>
      <gameName>TYPHOON</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>PHANTOM</audioNameHash>
      <layout>LAYOUT_TRUCK_BARRACKS</layout>
      <coverBoundOffsets>BARRACKS_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_TRUCK</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>BOX_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_LOW</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA_NO_REVERSE_LOOKAROUND_LOW</povCameraName>
      <FirstPersonDriveByIKOffset x="0.063000" y="-0.155000" z="-0.006000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.033000" y="-0.088000" z="-0.070000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.092000" y="0.035000" z="-0.080000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.028000" z="-0.078000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.028000" z="-0.078000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="-0.050000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.018000" y="-0.045000" z="-0.023000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="-0.050000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="-0.108000" z="-0.013000" />
	  <FirstPersonMobilePhoneOffset x="0.133000" y="0.360000" z="0.423000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.213000" y="0.340000" z="0.420000" />
      <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.163000" y="0.358000" z="0.573000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.376000" y="0.378000" z="0.620000" />
			<SeatIndex value="3" />
		</Item>
        <Item>
			<Offset x="0.163000" y="0.358000" z="0.573000" />
			<SeatIndex value="4" />
		</Item>
		<Item>
			<Offset x="0.376000" y="0.378000" z="0.620000" />
			<SeatIndex value="5" />
		</Item>
        <Item>
			<Offset x="0.163000" y="0.358000" z="0.573000" />
			<SeatIndex value="6" />
		</Item>
		<Item>
			<Offset x="0.376000" y="0.378000" z="0.620000" />
			<SeatIndex value="7" />
		</Item>
        <Item>
			<Offset x="0.163000" y="0.358000" z="0.573000" />
			<SeatIndex value="8" />
		</Item>
		<Item>
			<Offset x="0.376000" y="0.378000" z="0.620000" />
			<SeatIndex value="9" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="-0.020000" y="-0.025000" z="0.545000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.100000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.100000" />
      <vfxInfoName>VFXVEHICLEINFO_TRUCK_RIG</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="false" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.382700" />
      <wheelScaleRear value="0.382700" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        500.000000	
        500.000000	
        500.000000	
        500.000000	
        500.000000	
        500.000000
      </lodDistances>
      <minSeatHeight value="0.912" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="2500.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_BIG FLAG_HAS_LIVERY FLAG_AVOID_TURNS FLAG_USE_FAT_INTERIOR_LIGHT FLAG_IS_BULKY FLAG_BLOCK_FROM_ATTRACTOR_SCENARIO FLAG_HAS_INTERIOR_EXTRAS FLAG_HAS_BULLET_RESISTANT_GLASS FLAG_HAS_CAPPED_EXPLOSION_DAMAGE FLAG_ATTACH_TRAILER_ON_HIGHWAY FLAG_ATTACH_TRAILER_IN_CITY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
      <dashboardType>VDT_BANSHEE</dashboardType>
      <vehicleClass>VC_MILITARY</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
      </trailers>
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>S_M_M_Marine_01</driverName>
          <npcName />
        </Item>
        <Item>
          <driverName>S_M_Y_Marine_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BONNET</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>TRUCK_BARRACKS_FRONT_LEFT</Item>
        <Item>TRUCK_FRONT_RIGHT</Item>
        <Item>TRUCK_REVERSE_FRONT_LEFT</Item>
        <Item>TRUCK_REVERSE_FRONT_RIGHT</Item>
        <Item>TRUCK_REVERSE_FRONT_LEFT</Item>
        <Item>TRUCK_REVERSE_FRONT_RIGHT</Item>
        <Item>TRUCK_REVERSE_FRONT_LEFT</Item>
        <Item>TRUCK_REVERSE_FRONT_RIGHT</Item>
        <Item>TRUCK_REVERSE_FRONT_LEFT</Item>
        <Item>TRUCK_REVERSE_FRONT_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>

    <Item>
      <modelName>annihilator</modelName>
      <txdName>annihilator</txdName>
      <handlingId>ANNIHL</handlingId>
      <gameName>ANNIHL</gameName>
      <vehicleMakeName />
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash />
      <layout>LAYOUT_HELI_ANNIHILATOR</layout>
      <coverBoundOffsets>ANNIHILATOR_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_HELI_CAMERA</cameraName>
      <aimCameraName>HELI_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>ANNIHILATOR_BONNET_CAMERA</bonnetCameraName>
      <povCameraName>ANNIHILATOR_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.035000" y="-0.215000" z="-0.018000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.040000" z="-0.128000" />
      <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.050000" z="-0.035000" />
      <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.040000" z="-0.128000" />
      <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
      <FirstPersonMobilePhoneOffset x="0.190000" y="0.345000" z="0.490000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.190000" y="0.283000" z="0.448000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
            <Offset x="-0.504000" y="-0.143000" z="0.478000" />
            <SeatIndex value="2" />
        </Item>
        <Item>
            <Offset x="0.599000" y="-0.448000" z="0.466000" />
            <SeatIndex value="3" />
        </Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.050000" z="0.625000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.030000" z="0.055000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.030000" z="0.055000" />
      <vfxInfoName>VFXVEHICLEINFO_HELI_ANNIHILATOR</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.200100" />
      <wheelScaleRear value="0.117600" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.450000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="0.200000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="0.200000" />
      <damageMapScale value="0.300000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        25.000000
        35.000000
        100.000000
        200.000000
        1000.000000
        1000.000000
      </lodDistances>
      <minSeatHeight value="0.969" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.500000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_4</swankness>
      <maxNum value="999" />
      <flags>FLAG_NO_BOOT FLAG_DRIVER_NO_DRIVE_BY FLAG_NO_RESPRAY FLAG_ALLOWS_RAPPEL FLAG_DONT_SPAWN_IN_CARGEN FLAG_DONT_SPAWN_AS_AMBIENT FLAG_DONT_TIMESLICE_WHEELS FLAG_DISABLE_WEAPON_WHEEL_IN_FIRST_PERSON FLAG_USE_PILOT_HELMET</flags>
      <type>VEHICLE_TYPE_HELI</type>
      <plateType>VPT_NONE</plateType>
      <dashboardType>VDT_MAVERICK</dashboardType>
      <vehicleClass>VC_HELICOPTER</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers>
        <Item>
          <driverName>s_m_y_swat_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="true" />
      <requiredExtras />
      <rewards>
      </rewards>
      <cinematicPartCamera>
        <Item>HELI_REAR_ROTOR_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="-0.500000" />
      <buoyancySphereSizeScale value="0.900000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>STD_FRONT_LEFT</Item>
        <Item>HELI_ANNIHILATOR_FRONT_RIGHT</Item>
        <Item>HELI_LEFT_SIDE_PASSENGER</Item>
        <Item>HELI_RIGHT_SIDE_PASSENGER</Item>
        <Item>HELI_FROGGER_REAR_LEFT</Item>
        <Item>HELI_FROGGER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
      <lockOnPositionOffset x="0.000000" y="0.525000" z="-1.215000" />
    </Item>

    </InitDatas>
  <txdRelationships>

    <Item>
    </Item>

    <Item>
    </Item>

    <Item>
      <parent>vehicles_cav_interior</parent>
      <child>rcmcqueen</child>
    </Item>

    <Item>
      <parent>vehshare_worn</parent>
      <child>Kart3</child>
    </Item>

    <Item>
      <parent>vehicles_jet_interior</parent>
      <child>kj2000</child>
    </Item>
    <Item>
      <parent>vehicles_monster_interior</parent>
      <child>winkyc</child>
    </Item>
    <Item>
      <parent>vehicles_jet_interior</parent>
      <child>kj2000</child>
    </Item>
    <Item>
      <parent>vehicles_jet_interior</parent>
      <child>BELL360</child>
    </Item>
    <Item>
      <parent>vehicles_jet_interior</parent>
      <child>mh60l1</child>
    </Item>
    <Item>
      <parent>mh60l1</parent>
      <child>mh60l</child>
    </Item>
    <Item>
      <parent>vehicles_sultan_interior</parent>
      <child>mlec</child>
    </Item>
  </txdRelationships>

</CVehicleModelInfo__InitDataList>