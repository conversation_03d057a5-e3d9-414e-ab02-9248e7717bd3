* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: transparent;
  color: #ffffff;
  overflow: hidden;
}

/* Admin Panel Container */
#admin-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 1200px;
  height: 80vh;
  background: linear-gradient(135deg, rgba(10, 10, 10, 0.98) 0%, rgba(20, 20, 20, 0.98) 100%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 15px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.8);
  display: none;
  overflow: hidden;
}

/* Panel Header */
.panel-header {
  background: rgba(0, 0, 0, 0.5);
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid rgba(255, 215, 0, 0.2);
}

.panel-header h1 {
  font-size: 28px;
  color: #FFD700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
}

.close-btn {
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid #ff0000;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #ff0000;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 0, 0, 0.4);
  transform: scale(1.1);
}

/* Panel Content */
.panel-content {
  padding: 20px;
  height: calc(100% - 80px);
  overflow-y: auto;
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 20px;
}

/* Sections */
.section {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
}

.section h2 {
  font-size: 20px;
  color: #FFD700;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* KOTH Control Section */
.koth-control {
  height: fit-content;
}

.control-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  align-items: center;
}

.round-status {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-label {
  color: #888;
  font-size: 14px;
}

.status-value {
  font-weight: bold;
  color: #ff4444;
}

.status-value.active {
  color: #44ff44;
}

.zone-info {
  margin-top: 20px;
}

.info-item {
  margin-bottom: 15px;
}

.info-label {
  color: #888;
  font-size: 14px;
  display: block;
  margin-bottom: 5px;
}

.info-value {
  font-size: 16px;
  font-weight: bold;
}

.zone-points {
  display: flex;
  gap: 15px;
}

.team-points {
  font-size: 14px;
  font-weight: bold;
}

.team-points.red { color: #ff4444; }
.team-points.green { color: #44ff44; }
.team-points.blue { color: #4444ff; }

/* Player Management Section */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.search-bar {
  margin-bottom: 15px;
}

#player-search {
  width: 100%;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  color: white;
  font-size: 14px;
}

#player-search::placeholder {
  color: #666;
}

/* Player List */
.player-list {
  max-height: calc(80vh - 250px);
  overflow-y: auto;
}

.player-item {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.player-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 215, 0, 0.3);
  transform: translateX(5px);
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.player-name {
  font-size: 16px;
  font-weight: bold;
  color: #FFD700;
}

.player-id {
  font-size: 12px;
  color: #888;
}

.player-stats-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  font-size: 12px;
}

.stat {
  display: flex;
  flex-direction: column;
}

.stat-label {
  color: #666;
  font-size: 10px;
  text-transform: uppercase;
}

.stat-value {
  color: #fff;
  font-weight: bold;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
  color: white;
}

.btn-small {
  padding: 5px 10px;
  font-size: 12px;
}

.btn-refresh {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: linear-gradient(135deg, rgba(10, 10, 10, 0.98) 0%, rgba(20, 20, 20, 0.98) 100%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 15px;
  width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  background: rgba(0, 0, 0, 0.5);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  color: #FFD700;
  font-size: 20px;
}

.modal-body {
  padding: 20px;
}

.player-stats {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.action-section {
  margin-bottom: 20px;
}

.action-section h4 {
  color: #FFD700;
  font-size: 16px;
  margin-bottom: 10px;
}

.input-group {
  display: flex;
  gap: 10px;
}

.input-group input {
  flex: 1;
  padding: 10px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  color: white;
  font-size: 14px;
}

.danger-zone {
  border-top: 1px solid rgba(255, 0, 0, 0.3);
  padding-top: 20px;
}

/* Notifications */
#notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
}

.notification {
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 10px;
  min-width: 300px;
  animation: slideIn 0.3s ease-out;
  border-left: 4px solid;
}

.notification.success {
  border-color: #28a745;
}

.notification.error {
  border-color: #dc3545;
}

.notification.info {
  border-color: #007bff;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.5);
}

/* Responsive */
@media (max-width: 1200px) {
  .panel-content {
    grid-template-columns: 1fr;
  }
  
  .koth-control {
    margin-bottom: 20px;
  }
}
