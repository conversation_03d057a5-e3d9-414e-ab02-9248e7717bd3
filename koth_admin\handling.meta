<?xml version="1.0" encoding="UTF-8"?>
<CHandlingDataMgr>
  <HandlingData>
    
    <!-- APC - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>APC</handlingName>
      <fMass value="6000.000000" />
      <fInitialDragCoeff value="8.500000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="5" />
      <fInitialDriveForce value="1.200000" />
      <fDriveInertia value="0.500000" />
      <fClutchChangeRateScaleUpShift value="5.000000" />
      <fClutchChangeRateScaleDownShift value="5.000000" />
      <fInitialDriveMaxFlatVel value="150.000000" />
      <fBrakeForce value="1.200000" />
      <fBrakeBiasFront value="0.500000" />
      <fHandBrakeForce value="0.800000" />
      <fSteeringLock value="35.000000" />
      <fTractionCurveMax value="2.550000" />
      <fTractionCurveMin value="2.350000" />
      <fTractionCurveLateral value="22.500000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.485000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="2.200000" />
      <fSuspensionCompDamp value="1.400000" />
      <fSuspensionReboundDamp value="2.200000" />
      <fSuspensionUpperLimit value="0.100000" />
      <fSuspensionLowerLimit value="-0.120000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.520000" />
      <fAntiRollBarForce value="0.800000" />
      <fAntiRollBarBiasFront value="0.600000" />
      <fRollCentreHeightFront value="0.340000" />
      <fRollCentreHeightRear value="0.340000" />
      <fCollisionDamageMult value="0.700000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="0.700000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="65.000000" />
      <fOilVolume value="5.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="150000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>AVERAGE</AIHandling>
      <SubHandlingData>
        <Item type="CCarHandlingData">
          <fBackEndPopUpCarImpulseMult value="0.100000" />
          <fBackEndPopUpBuildingImpulseMult value="0.030000" />
          <fBackEndPopUpMaxDeltaSpeed value="0.600000" />
        </Item>
        <Item type="CVehicleWeaponHandlingData">
          <fWeaponDamageMult value="1.000000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- MLEC - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>MLEC</handlingName>
      <fMass value="1600.000000" />
      <fInitialDragCoeff value="3.000000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="5" />
      <fInitialDriveForce value="0.390000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="3.100000" />
      <fClutchChangeRateScaleDownShift value="3.100000" />
      <fInitialDriveMaxFlatVel value="195.000000" />
      <fBrakeForce value="1.000000" />
      <fBrakeBiasFront value="0.500000" />
      <fHandBrakeForce value="0.800000" />
      <fSteeringLock value="40.000000" />
      <fTractionCurveMax value="2.550000" />
      <fTractionCurveMin value="2.350000" />
      <fTractionCurveLateral value="22.500000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.485000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="2.200000" />
      <fSuspensionCompDamp value="1.400000" />
      <fSuspensionReboundDamp value="2.200000" />
      <fSuspensionUpperLimit value="0.100000" />
      <fSuspensionLowerLimit value="-0.120000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.520000" />
      <fAntiRollBarForce value="0.800000" />
      <fAntiRollBarBiasFront value="0.600000" />
      <fRollCentreHeightFront value="0.340000" />
      <fRollCentreHeightRear value="0.340000" />
      <fCollisionDamageMult value="0.700000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="0.700000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="65.000000" />
      <fOilVolume value="5.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="35000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>AVERAGE</AIHandling>
      <SubHandlingData>
        <Item type="CCarHandlingData">
          <fBackEndPopUpCarImpulseMult value="0.100000" />
          <fBackEndPopUpBuildingImpulseMult value="0.030000" />
          <fBackEndPopUpMaxDeltaSpeed value="0.600000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- T90MS Tank - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>t90ms</handlingName>
      <fMass value="8000.000000" />
      <fInitialDragCoeff value="10.000000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="5" />
      <fInitialDriveForce value="0.520000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="2.300000" />
      <fClutchChangeRateScaleDownShift value="2.300000" />
      <fInitialDriveMaxFlatVel value="78.000000" />
      <fBrakeForce value="1.500000" />
      <fBrakeBiasFront value="0.500000" />
      <fHandBrakeForce value="0.800000" />
      <fSteeringLock value="30.000000" />
      <fTractionCurveMax value="2.750000" />
      <fTractionCurveMin value="2.550000" />
      <fTractionCurveLateral value="25.000000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.485000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="2.500000" />
      <fSuspensionCompDamp value="1.600000" />
      <fSuspensionReboundDamp value="2.400000" />
      <fSuspensionUpperLimit value="0.080000" />
      <fSuspensionLowerLimit value="-0.100000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.520000" />
      <fAntiRollBarForce value="1.000000" />
      <fAntiRollBarBiasFront value="0.600000" />
      <fRollCentreHeightFront value="0.340000" />
      <fRollCentreHeightRear value="0.340000" />
      <fCollisionDamageMult value="0.500000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="0.500000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="100.000000" />
      <fOilVolume value="8.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="200000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>AVERAGE</AIHandling>
      <SubHandlingData>
        <Item type="CCarHandlingData">
          <fBackEndPopUpCarImpulseMult value="0.050000" />
          <fBackEndPopUpBuildingImpulseMult value="0.020000" />
          <fBackEndPopUpMaxDeltaSpeed value="0.400000" />
        </Item>
        <Item type="CVehicleWeaponHandlingData">
          <fWeaponDamageMult value="1.000000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- MH60L Helicopter - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>MH60L</handlingName>
      <fMass value="8000.000000" />
      <fInitialDragCoeff value="0.100000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="1" />
      <fInitialDriveForce value="1.000000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="5.000000" />
      <fClutchChangeRateScaleDownShift value="5.000000" />
      <fInitialDriveMaxFlatVel value="195.000000" />
      <fBrakeForce value="0.800000" />
      <fBrakeBiasFront value="0.000000" />
      <fHandBrakeForce value="0.500000" />
      <fSteeringLock value="22.000000" />
      <fTractionCurveMax value="2.500000" />
      <fTractionCurveMin value="2.000000" />
      <fTractionCurveLateral value="17.500000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.500000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="2.000000" />
      <fSuspensionCompDamp value="1.000000" />
      <fSuspensionReboundDamp value="1.000000" />
      <fSuspensionUpperLimit value="0.100000" />
      <fSuspensionLowerLimit value="-0.100000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.500000" />
      <fAntiRollBarForce value="0.000000" />
      <fAntiRollBarBiasFront value="0.500000" />
      <fRollCentreHeightFront value="0.500000" />
      <fRollCentreHeightRear value="0.500000" />
      <fCollisionDamageMult value="1.000000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="1.000000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="100.000000" />
      <fOilVolume value="10.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="2000000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>HELI</AIHandling>
      <SubHandlingData>
        <Item type="CFlyingHandlingData">
          <fThrust value="1.300000" />
          <fThrustFallOff value="0.700000" />
          <fThrustVectoring value="0.000000" />
          <fInitialThrust value="2.200000" />
          <fInitialThrustFallOff value="0.500000" />
          <fYawMult value="2.000000" />
          <fYawStabilise value="1.000000" />
          <fSideSlipMult value="1.000000" />
          <fInitialYawMult value="1.000000" />
          <fRollMult value="3.000000" />
          <fRollStabilise value="1.000000" />
          <fInitialRollMult value="1.000000" />
          <fPitchMult value="2.500000" />
          <fPitchStabilise value="0.500000" />
          <fInitialPitchMult value="1.000000" />
          <fFormLiftMult value="1.000000" />
          <fAttackLiftMult value="0.300000" />
          <fAttackDiveMult value="0.500000" />
          <fGearDownDragV value="0.100000" />
          <fGearDownLiftMult value="1.000000" />
          <fWindMult value="1.000000" />
          <fMoveRes value="0.050000" />
          <vecTurnRes x="0.100000" y="0.100000" z="0.050000" />
          <vecSpeedRes x="0.000000" y="0.000000" z="0.000000" />
          <fGearDoorFrontOpen value="0.000000" />
          <fGearDoorRearOpen value="0.000000" />
          <fGearDoorRearOpen2 value="0.000000" />
          <fGearDoorRearMOpen value="0.000000" />
          <fTurbulenceForceMulti value="0.000000" />
          <fTurbulenceRollTorqueMulti value="0.000000" />
          <fTurbulencePitchTorqueMulti value="0.000000" />
          <fBodyDamageControlEffectMult value="1.000000" />
          <fInputSensitivityForDifficulty value="1.000000" />
          <fOnGroundYawBoostSpeedPeak value="0.000000" />
          <fOnGroundYawBoostSpeedCap value="0.000000" />
          <fEngineOffGlideMulti value="0.000100" />
          <fAfterburnerEffectRadius value="5.000000" />
          <fAfterburnerEffectDistance value="8.000000" />
          <fAfterburnerEffectForceMulti value="0.000000" />
          <fSubmergeLevelToPullHeliUnderwater value="45.000000" />
          <fExtraLiftWithRoll value="1.000000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- Bell360 Helicopter - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>bell360</handlingName>
      <fMass value="3500.000000" />
      <fInitialDragCoeff value="0.080000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="1" />
      <fInitialDriveForce value="1.000000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="5.000000" />
      <fClutchChangeRateScaleDownShift value="5.000000" />
      <fInitialDriveMaxFlatVel value="169.000000" />
      <fBrakeForce value="0.600000" />
      <fBrakeBiasFront value="0.000000" />
      <fHandBrakeForce value="0.400000" />
      <fSteeringLock value="22.000000" />
      <fTractionCurveMax value="2.300000" />
      <fTractionCurveMin value="1.800000" />
      <fTractionCurveLateral value="15.000000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.500000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="1.800000" />
      <fSuspensionCompDamp value="0.800000" />
      <fSuspensionReboundDamp value="0.800000" />
      <fSuspensionUpperLimit value="0.100000" />
      <fSuspensionLowerLimit value="-0.100000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.500000" />
      <fAntiRollBarForce value="0.000000" />
      <fAntiRollBarBiasFront value="0.500000" />
      <fRollCentreHeightFront value="0.500000" />
      <fRollCentreHeightRear value="0.500000" />
      <fCollisionDamageMult value="1.000000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="1.000000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="60.000000" />
      <fOilVolume value="5.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="800000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>HELI</AIHandling>
      <SubHandlingData>
        <Item type="CFlyingHandlingData">
          <fThrust value="1.100000" />
          <fThrustFallOff value="0.600000" />
          <fThrustVectoring value="0.000000" />
          <fInitialThrust value="1.800000" />
          <fInitialThrustFallOff value="0.400000" />
          <fYawMult value="1.800000" />
          <fYawStabilise value="1.000000" />
          <fSideSlipMult value="1.000000" />
          <fInitialYawMult value="1.000000" />
          <fRollMult value="2.500000" />
          <fRollStabilise value="1.000000" />
          <fInitialRollMult value="1.000000" />
          <fPitchMult value="2.000000" />
          <fPitchStabilise value="0.400000" />
          <fInitialPitchMult value="1.000000" />
          <fFormLiftMult value="1.000000" />
          <fAttackLiftMult value="0.250000" />
          <fAttackDiveMult value="0.400000" />
          <fGearDownDragV value="0.080000" />
          <fGearDownLiftMult value="1.000000" />
          <fWindMult value="1.000000" />
          <fMoveRes value="0.040000" />
          <vecTurnRes x="0.080000" y="0.080000" z="0.040000" />
          <vecSpeedRes x="0.000000" y="0.000000" z="0.000000" />
          <fGearDoorFrontOpen value="0.000000" />
          <fGearDoorRearOpen value="0.000000" />
          <fGearDoorRearOpen2 value="0.000000" />
          <fGearDoorRearMOpen value="0.000000" />
          <fTurbulenceForceMulti value="0.000000" />
          <fTurbulenceRollTorqueMulti value="0.000000" />
          <fTurbulencePitchTorqueMulti value="0.000000" />
          <fBodyDamageControlEffectMult value="1.000000" />
          <fInputSensitivityForDifficulty value="1.000000" />
          <fOnGroundYawBoostSpeedPeak value="0.000000" />
          <fOnGroundYawBoostSpeedCap value="0.000000" />
          <fEngineOffGlideMulti value="0.000100" />
          <fAfterburnerEffectRadius value="3.000000" />
          <fAfterburnerEffectDistance value="5.000000" />
          <fAfterburnerEffectForceMulti value="0.000000" />
          <fSubmergeLevelToPullHeliUnderwater value="45.000000" />
          <fExtraLiftWithRoll value="1.000000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- 17CHEYENNE2 Truck - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>17CHEYENNE2</handlingName>
      <fMass value="2200.000000" />
      <fInitialDragCoeff value="4.500000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="5" />
      <fInitialDriveForce value="0.390000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="2.800000" />
      <fClutchChangeRateScaleDownShift value="2.800000" />
      <fInitialDriveMaxFlatVel value="169.000000" />
      <fBrakeForce value="1.200000" />
      <fBrakeBiasFront value="0.500000" />
      <fHandBrakeForce value="0.800000" />
      <fSteeringLock value="35.000000" />
      <fTractionCurveMax value="2.400000" />
      <fTractionCurveMin value="2.200000" />
      <fTractionCurveLateral value="20.000000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.485000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="2.000000" />
      <fSuspensionCompDamp value="1.200000" />
      <fSuspensionReboundDamp value="2.000000" />
      <fSuspensionUpperLimit value="0.120000" />
      <fSuspensionLowerLimit value="-0.140000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.520000" />
      <fAntiRollBarForce value="0.600000" />
      <fAntiRollBarBiasFront value="0.600000" />
      <fRollCentreHeightFront value="0.340000" />
      <fRollCentreHeightRear value="0.340000" />
      <fCollisionDamageMult value="0.800000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="0.800000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="80.000000" />
      <fOilVolume value="6.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="45000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>TRUCK</AIHandling>
      <SubHandlingData>
        <Item type="CCarHandlingData">
          <fBackEndPopUpCarImpulseMult value="0.120000" />
          <fBackEndPopUpBuildingImpulseMult value="0.040000" />
          <fBackEndPopUpMaxDeltaSpeed value="0.700000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- cfmotoz10 Motorcycle - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>cfmotoz10</handlingName>
      <fMass value="180.000000" />
      <fInitialDragCoeff value="1.500000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="5" />
      <fInitialDriveForce value="0.520000" />
      <fDriveInertia value="0.800000" />
      <fClutchChangeRateScaleUpShift value="4.000000" />
      <fClutchChangeRateScaleDownShift value="4.000000" />
      <fInitialDriveMaxFlatVel value="221.000000" />
      <fBrakeForce value="1.500000" />
      <fBrakeBiasFront value="0.650000" />
      <fHandBrakeForce value="0.600000" />
      <fSteeringLock value="35.000000" />
      <fTractionCurveMax value="2.800000" />
      <fTractionCurveMin value="2.600000" />
      <fTractionCurveLateral value="18.000000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="0.800000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.485000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="1.800000" />
      <fSuspensionCompDamp value="1.000000" />
      <fSuspensionReboundDamp value="1.800000" />
      <fSuspensionUpperLimit value="0.080000" />
      <fSuspensionLowerLimit value="-0.100000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.500000" />
      <fAntiRollBarForce value="0.000000" />
      <fAntiRollBarBiasFront value="0.500000" />
      <fRollCentreHeightFront value="0.340000" />
      <fRollCentreHeightRear value="0.340000" />
      <fCollisionDamageMult value="1.200000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="1.200000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="15.000000" />
      <fOilVolume value="2.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="25000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>BIKE</AIHandling>
      <SubHandlingData>
        <Item type="CBikeHandlingData">
          <fLeanFwdCOMMult value="1.000000" />
          <fLeanFwdForceMult value="0.300000" />
          <fLeanBakCOMMult value="1.000000" />
          <fLeanBakForceMult value="0.200000" />
          <fMaxBankAngle value="60.000000" />
          <fFullAnimAngle value="50.000000" />
          <fDesLeanReturnFrac value="0.900000" />
          <fStickLeanMult value="2.000000" />
          <fBrakingStabilityMult value="2.500000" />
          <fInAirSteerMult value="0.800000" />
          <fWheelieBalancePoint value="0.700000" />
          <fStoppieBalancePoint value="0.000000" />
          <fWheelieSteerMult value="0.000000" />
          <fRearBalanceMult value="0.000000" />
          <fFrontBalanceMult value="0.000000" />
          <fBikeGroundSideFrictionMult value="0.800000" />
          <fBikeWheelGroundSideFrictionMult value="1.000000" />
          <fBikeOnStandLeanAngle value="15.000000" />
          <fBikeOnStandSteerAngle value="25.000000" />
          <fJumpForce value="0.600000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- WINKYC Clean Winky - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>WINKYC</handlingName>
      <fMass value="1800.000000" />
      <fInitialDragCoeff value="3.500000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="5" />
      <fInitialDriveForce value="0.390000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="2.800000" />
      <fClutchChangeRateScaleDownShift value="2.800000" />
      <fInitialDriveMaxFlatVel value="156.000000" />
      <fBrakeForce value="1.000000" />
      <fBrakeBiasFront value="0.500000" />
      <fHandBrakeForce value="0.800000" />
      <fSteeringLock value="40.000000" />
      <fTractionCurveMax value="2.600000" />
      <fTractionCurveMin value="2.400000" />
      <fTractionCurveLateral value="22.000000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.200000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.485000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="2.400000" />
      <fSuspensionCompDamp value="1.600000" />
      <fSuspensionReboundDamp value="2.400000" />
      <fSuspensionUpperLimit value="0.150000" />
      <fSuspensionLowerLimit value="-0.180000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.520000" />
      <fAntiRollBarForce value="0.400000" />
      <fAntiRollBarBiasFront value="0.600000" />
      <fRollCentreHeightFront value="0.340000" />
      <fRollCentreHeightRear value="0.340000" />
      <fCollisionDamageMult value="0.900000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="0.900000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="60.000000" />
      <fOilVolume value="5.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="30000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>AVERAGE</AIHandling>
      <SubHandlingData>
        <Item type="CCarHandlingData">
          <fBackEndPopUpCarImpulseMult value="0.100000" />
          <fBackEndPopUpBuildingImpulseMult value="0.030000" />
          <fBackEndPopUpMaxDeltaSpeed value="0.600000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- pavelow Helicopter - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>pavelow</handlingName>
      <fMass value="12000.000000" />
      <fInitialDragCoeff value="0.120000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="1" />
      <fInitialDriveForce value="1.000000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="5.000000" />
      <fClutchChangeRateScaleDownShift value="5.000000" />
      <fInitialDriveMaxFlatVel value="182.000000" />
      <fBrakeForce value="1.000000" />
      <fBrakeBiasFront value="0.000000" />
      <fHandBrakeForce value="0.600000" />
      <fSteeringLock value="22.000000" />
      <fTractionCurveMax value="2.700000" />
      <fTractionCurveMin value="2.200000" />
      <fTractionCurveLateral value="20.000000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.500000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="2.200000" />
      <fSuspensionCompDamp value="1.200000" />
      <fSuspensionReboundDamp value="1.200000" />
      <fSuspensionUpperLimit value="0.100000" />
      <fSuspensionLowerLimit value="-0.100000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.500000" />
      <fAntiRollBarForce value="0.000000" />
      <fAntiRollBarBiasFront value="0.500000" />
      <fRollCentreHeightFront value="0.500000" />
      <fRollCentreHeightRear value="0.500000" />
      <fCollisionDamageMult value="0.800000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="0.800000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="150.000000" />
      <fOilVolume value="15.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="3000000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>HELI</AIHandling>
      <SubHandlingData>
        <Item type="CFlyingHandlingData">
          <fThrust value="1.400000" />
          <fThrustFallOff value="0.800000" />
          <fThrustVectoring value="0.000000" />
          <fInitialThrust value="2.400000" />
          <fInitialThrustFallOff value="0.600000" />
          <fYawMult value="2.200000" />
          <fYawStabilise value="1.000000" />
          <fSideSlipMult value="1.000000" />
          <fInitialYawMult value="1.000000" />
          <fRollMult value="3.200000" />
          <fRollStabilise value="1.000000" />
          <fInitialRollMult value="1.000000" />
          <fPitchMult value="2.800000" />
          <fPitchStabilise value="0.600000" />
          <fInitialPitchMult value="1.000000" />
          <fFormLiftMult value="1.000000" />
          <fAttackLiftMult value="0.350000" />
          <fAttackDiveMult value="0.600000" />
          <fGearDownDragV value="0.120000" />
          <fGearDownLiftMult value="1.000000" />
          <fWindMult value="1.000000" />
          <fMoveRes value="0.060000" />
          <vecTurnRes x="0.120000" y="0.120000" z="0.060000" />
          <vecSpeedRes x="0.000000" y="0.000000" z="0.000000" />
          <fGearDoorFrontOpen value="0.000000" />
          <fGearDoorRearOpen value="0.000000" />
          <fGearDoorRearOpen2 value="0.000000" />
          <fGearDoorRearMOpen value="0.000000" />
          <fTurbulenceForceMulti value="0.000000" />
          <fTurbulenceRollTorqueMulti value="0.000000" />
          <fTurbulencePitchTorqueMulti value="0.000000" />
          <fBodyDamageControlEffectMult value="1.000000" />
          <fInputSensitivityForDifficulty value="1.000000" />
          <fOnGroundYawBoostSpeedPeak value="0.000000" />
          <fOnGroundYawBoostSpeedCap value="0.000000" />
          <fEngineOffGlideMulti value="0.000100" />
          <fAfterburnerEffectRadius value="6.000000" />
          <fAfterburnerEffectDistance value="10.000000" />
          <fAfterburnerEffectForceMulti value="0.000000" />
          <fSubmergeLevelToPullHeliUnderwater value="45.000000" />
          <fExtraLiftWithRoll value="1.000000" />
        </Item>
      </SubHandlingData>
    </Item>

    <!-- PBUS Prison Bus - 30% Speed Increase -->
    <Item type="CHandlingData">
      <handlingName>PBUS</handlingName>
      <fMass value="8000.000000" />
      <fInitialDragCoeff value="8.000000" />
      <fPercentSubmerged value="85.000000" />
      <vecCentreOfMassOffset x="0.000000" y="0.000000" z="0.000000" />
      <vecInertiaMultiplier x="1.000000" y="1.000000" z="1.000000" />
      <fDriveBiasFront value="0.000000" />
      <nInitialDriveGears value="5" />
      <fInitialDriveForce value="0.350000" />
      <fDriveInertia value="1.000000" />
      <fClutchChangeRateScaleUpShift value="2.000000" />
      <fClutchChangeRateScaleDownShift value="2.000000" />
      <fInitialDriveMaxFlatVel value="130.000000" />
      <fBrakeForce value="1.500000" />
      <fBrakeBiasFront value="0.500000" />
      <fHandBrakeForce value="1.000000" />
      <fSteeringLock value="30.000000" />
      <fTractionCurveMax value="2.200000" />
      <fTractionCurveMin value="2.000000" />
      <fTractionCurveLateral value="18.000000" />
      <fTractionSpringDeltaMax value="0.150000" />
      <fLowSpeedTractionLossMult value="1.000000" />
      <fCamberStiffnesss value="0.000000" />
      <fTractionBiasFront value="0.485000" />
      <fTractionLossMult value="1.000000" />
      <fSuspensionForce value="2.800000" />
      <fSuspensionCompDamp value="1.800000" />
      <fSuspensionReboundDamp value="2.800000" />
      <fSuspensionUpperLimit value="0.080000" />
      <fSuspensionLowerLimit value="-0.100000" />
      <fSuspensionRaise value="0.000000" />
      <fSuspensionBiasFront value="0.520000" />
      <fAntiRollBarForce value="1.200000" />
      <fAntiRollBarBiasFront value="0.600000" />
      <fRollCentreHeightFront value="0.340000" />
      <fRollCentreHeightRear value="0.340000" />
      <fCollisionDamageMult value="0.600000" />
      <fWeaponDamageMult value="1.000000" />
      <fDeformationDamageMult value="0.600000" />
      <fEngineDamageMult value="1.500000" />
      <fPetrolTankVolume value="120.000000" />
      <fOilVolume value="10.000000" />
      <fSeatOffsetDistX value="0.000000" />
      <fSeatOffsetDistY value="0.000000" />
      <fSeatOffsetDistZ value="0.000000" />
      <nMonetaryValue value="80000" />
      <strModelFlags>440010</strModelFlags>
      <strHandlingFlags>0</strHandlingFlags>
      <strDamageFlags>0</strDamageFlags>
      <AIHandling>TRUCK</AIHandling>
      <SubHandlingData>
        <Item type="CCarHandlingData">
          <fBackEndPopUpCarImpulseMult value="0.080000" />
          <fBackEndPopUpBuildingImpulseMult value="0.025000" />
          <fBackEndPopUpMaxDeltaSpeed value="0.500000" />
        </Item>
      </SubHandlingData>
    </Item>

  </HandlingData>
</CHandlingDataMgr>
