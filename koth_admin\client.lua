print('[KOTH Admin] Client loading...')

local adminPanelOpen = false

-- Open admin panel
RegisterNetEvent('koth_admin:openPanel', function(data)
    print('[KOTH Admin] Opening admin panel')
    
    if data then
        SendNUIMessage({
            action = 'openPanel',
            players = data.players,
            kothStatus = data.kothStatus
        })
        
        SetNuiFocus(true, true)
        adminPanelOpen = true
    end
end)

-- Update panel data
RegisterNetEvent('koth_admin:updateData', function(data)
    if adminPanelOpen and data then
        SendNUIMessage({
            action = 'updateData',
            players = data.players,
            kothStatus = data.kothStatus
        })
    end
end)

-- Show notification
RegisterNetEvent('koth_admin:notification', function(type, message)
    SendNUIMessage({
        action = 'notification',
        type = type,
        message = message
    })
end)

-- NUI Callbacks

-- Close panel
RegisterNUICallback('closePanel', function(data, cb)
    SetNuiFocus(false, false)
    adminPanelOpen = false
    cb('ok')
end)

-- Give money
RegisterNUICallback('giveMoney', function(data, cb)
    if data.playerId and data.amount then
        TriggerServerEvent('koth_admin:giveMoney', data.playerId, data.amount)
    end
    cb('ok')
end)

-- Give XP
RegisterNUICallback('giveXP', function(data, cb)
    if data.playerId and data.amount then
        TriggerServerEvent('koth_admin:giveXP', data.playerId, data.amount)
    end
    cb('ok')
end)

-- Set level
RegisterNUICallback('setLevel', function(data, cb)
    if data.playerId and data.level then
        TriggerServerEvent('koth_admin:setLevel', data.playerId, data.level)
    end
    cb('ok')
end)

-- Start round
RegisterNUICallback('startRound', function(data, cb)
    TriggerServerEvent('koth_admin:startRound')
    cb('ok')
end)

-- Stop round
RegisterNUICallback('stopRound', function(data, cb)
    TriggerServerEvent('koth_admin:stopRound')
    cb('ok')
end)

-- Reset player
RegisterNUICallback('resetPlayer', function(data, cb)
    if data.playerId then
        TriggerServerEvent('koth_admin:resetPlayer', data.playerId)
    end
    cb('ok')
end)

-- Refresh players
RegisterNUICallback('refreshPlayers', function(data, cb)
    TriggerServerEvent('koth_admin:refreshPlayers')
    cb('ok')
end)

-- Delete all vehicles
RegisterNUICallback('deleteAllVehicles', function(data, cb)
    TriggerServerEvent('koth_admin:deleteAllVehicles')
    cb('ok')
end)

-- Handle ESC key
Citizen.CreateThread(function()
    while true do
        if adminPanelOpen then
            if IsControlJustReleased(0, 322) then -- ESC key
                SendNUIMessage({ action = 'closePanel' })
                SetNuiFocus(false, false)
                adminPanelOpen = false
            end
        end
        Citizen.Wait(0)
    end
end)

print('[KOTH Admin] Client loaded successfully')
